/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './public/offline.html',
    './src/**/*.{js,ts,jsx,tsx,vue}',
    'index.html',
  ],
  future: {
    hoverOnlyWhenSupported: true,
  },
  theme: {
    extend: {
      colors: {
        'tavoos-orange': 'var(--tavoos-orange)',
      },
      maxHeight: {
        112: '28rem',
        128: '32rem',
        144: '36rem',
        160: '40rem',
      },
    },
  },
  variants: {
    extend: {
      display: ['portrait', 'landscape'],
    },
  },
  plugins: [
    require('@tailwindcss/aspect-ratio'),
    require('@tailwindcss/container-queries'),
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),],
};
