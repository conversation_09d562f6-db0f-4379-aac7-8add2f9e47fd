import { defineStore } from 'pinia';
import Continent from '@/models/Continent.ts';

export const useContinentStore = defineStore('ContinentStore', {
  state: () => ({
    continents: [
      {
        id: 1,
        code: 'AF',
        name: 'Africa',
      },
      {
        id: 2,
        code: 'AN',
        name: 'Antractica',
      },
      {
        id: 3,
        code: 'AS',
        name: 'Asia',
      },
      {
        id: 4,
        code: 'EU',
        name: 'Europe',
      },
      {
        id: 5,
        code: 'NA',
        name: 'North America',
      },
      {
        id: 6,
        code: 'OC',
        name: 'Oceania',
      },
      {
        id: 7,
        code: 'SA',
        name: 'South America',
      },
    ].map(data => new Continent(data)) as Continent[],
  }),
});
