import { defineStore } from 'pinia';
import type Country from '@/models/Country.ts';

export const useCountryStore = defineStore('CountryStore', {
  state: () => ({
    countries: [] as Country[],
  }),

  getters: {
    numberOfCountries: state => state.countries.length,
  },

  actions: {
    addCountry(country: Country) {
      if (this.countries.some(({ id }) => id == country.id)) return;

      this.countries.push(country as any);
    },
  },
});
