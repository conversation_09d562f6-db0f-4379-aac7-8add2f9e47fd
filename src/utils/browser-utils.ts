export default class BrowserUtils {
  static browserDetails() {
    const navUserAgent = navigator.userAgent;
    let browserName = navigator.appName;
    let browserVersion = `${parseFloat(navigator.appVersion)}`;
    const tempNameOffset = navUserAgent.lastIndexOf(' ') + 1;

    const tempVersionOffset = (browser: string) => navUserAgent.includes(browser);

    if (tempVersionOffset('Opera')) {
      browserName = 'Opera';
      browserVersion = tempVersionOffset('Version') ? navUserAgent.substring(8) : navUserAgent.substring(6);
    } else if (tempVersionOffset('MSIE')) {
      browserName = 'Microsoft Internet Explorer';
      browserVersion = navUserAgent.substring(5);
    } else if (tempVersionOffset('Chrome')) {
      browserName = 'Chrome';
      browserVersion = navUserAgent.substring(7);
    } else if (tempVersionOffset('Safari')) {
      browserName = 'Safari';
      browserVersion = tempVersionOffset('Version') ? navUserAgent.substring(8) : navUserAgent.substring(7);
    } else if (tempVersionOffset('Firefox')) {
      browserName = 'Firefox';
      browserVersion = navUserAgent.substring(8);
    } else if (tempNameOffset < navUserAgent.lastIndexOf('/')) {
      browserName = navUserAgent.substring(tempNameOffset, navUserAgent.lastIndexOf('/'));
      browserVersion = navUserAgent.substring(navUserAgent.lastIndexOf('/') + 1);
      if (browserName.toLowerCase() == browserName.toUpperCase()) {
        browserName = navigator.appName;
      }
    }

    // trim version
    const semicolonPos = browserVersion.indexOf(';');
    if (semicolonPos != -1) {
      browserVersion = browserVersion.substring(0, semicolonPos);
    }

    const spacePos = browserVersion.indexOf(' ');
    if (spacePos != -1) {
      browserVersion = browserVersion.substring(0, spacePos);
    }

    return {
      browserName,
      browserVersion,
    };
  }
}
