import { IVerse } from "@/types/types-quran.ts";

export default class VerseUtils {
  static sortIds(ids: string[]) {
    return ids.sort(VerseUtils.compareIds);
  }

  static sortVerses(verses: IVerse[]) {
    return verses.sort((v, w) => v.indexInQuran < w.indexInQuran ? -1 : 1);
  }

  private static compareIds(id1: string, id2: string) {
    const [vChapter, vIndex] = id1.split(':').map(x => parseInt(x, 10));
    const [wChapter, wIndex] = id2.split(':').map(x => parseInt(x, 10));

    switch(true){
      case vChapter < wChapter:
        return -1;
      case vChapter > wChapter:
        return 1;
      case vIndex === wIndex:
        return 0;
      default:
        return vIndex < wIndex ? -1 : 1;
    }
  };
}

