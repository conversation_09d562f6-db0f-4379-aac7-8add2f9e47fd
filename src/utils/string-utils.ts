export default class StringUtils {
  /**
   * Check whether a character is a sound sign
   */
  private longVowelSigns = {
    oo: String.fromCharCode(1765),
    ee: String.fromCharCode(1766),
  };

  private shortVoWelSigns = {
    a: String.fromCharCode(1614),
    o: String.fromCharCode(1615),
    e: String.fromCharCode(1616),
  };

  private tanvinSigns = {
    an: String.fromCharCode(1611),
    on: String.fromCharCode(1612),
    en: String.fromCharCode(1613),
  };

  private otherSigns = {
    tashdid: String.fromCharCode(1617),
    sokoon: String.fromCharCode(1618),
    sali: String.fromCharCode(1750), // صلی
  };

  private hamzeSigns = {
    alephTop: String.fromCharCode(),
    alephBottom: String.fromCharCode(),
    onVav: String.fromCharCode(),
    OnYa: String.fromCharCode(),
    alone: String.fromCharCode(),
  };

  private signs = [
    ...Object.values(this.longVowelSigns),
    ...Object.values(this.shortVoWelSigns),
    ...Object.values(this.tanvinSigns),
    ...Object.values(this.otherSigns),
    ...Object.values(this.hamzeSigns),
  ];

  private substitutionMap: Record<number, string> = {
    // الف مقصوره
    1648: 'ا',
    1750: 'ا',
    // الف ممدوده
    1575: 'ا',
    // ی
    1766: 'ی',
  };

  private deletionList = [
    String.fromCharCode(1609),
    String.fromCharCode(1619),
    String.fromCharCode(1762),
    String.fromCharCode(1773),
    "'",
    '_',
    '.',
    '-',
    '*',
    ';',
    '"',
  ];

  private isAllowed(char: string) {
    return char.length === 1 && !this.deletionList.includes(char);
  }

  isSign(char: string) {
    return char.length === 1 && this.signs.includes(char);
  }

  /**
   * Replace arabic letters with their persian equivalents
   */
  persianize(str: string) {
    return str
      .replace(/أ|إ|آ|ٱ/g, 'ا')
      .replace(/ي/g, 'ی')
      .replace(/ك/g, 'ک')
      .replace(/ة/g, 'ه')
      .replace(/ۦ/g, '');
  }

  /**
   * Remove sound signs from a string
   */
  purify(text: string) {
    const purified = Array.from(text)
      .map(char => this.substitutionMap[char.charCodeAt(0)] || char)
      .filter(char => !this.isSign(char) && this.isAllowed(char))
      .join('');
    return this.persianize(purified);
  }

  /**
   * Strip all whitespaces out of a given string
   */
  zip(str: string) {
    return str.replace(/\s/g, '');
  }

  sliceByIndices(str: string, indices: number[]) {
    const result = [];
    let start = 0;

    for (const end of indices) {
      result.push(str.slice(start, end));
      start = end;
    }

    result.push(str.slice(start)); // Add the final chunk
    return result;
  }
}
