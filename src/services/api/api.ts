import { devMode } from '@/utils/utils.ts';
import TavoosService from '../service.ts';

export default class Api extends TavoosService {
  get host() {
    return devMode() ? 'http://localhost' : 'https://behnegar.app';
  }

  get baseURL() {
    return `${this.host}/api/v1`;
  }

  serviceURL(service: string, throughTavoos: Boolean = true) {
    return `${this.baseURL}/${throughTavoos ? 'tavoos/' : ''}${service}`;
  }

  get ahadithServiceURL() {
    return this.serviceURL('hadith', false);
  }

  get duaServiceURL() {
    return this.serviceURL('duas');
  }
}
