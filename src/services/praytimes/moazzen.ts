export default class Moazzen {
  static audio: HTMLAudioElement | null = null;
  static lastTime = 0;
  static pausedAt: number | null = null;

  static async playAzan() {
    if (Date.now() - (Moazzen.pausedAt ?? Infinity) > 5 * 60 * 1000) {
      Moazzen.pausedAt = null;
      Moazzen.lastTime = 0;
    }

    try {
      const url = '/sound/pray-times/azan/aghati.mp3';
      Moazzen.audio ||= new Audio(url);
      Moazzen.audio.currentTime = Moazzen.lastTime;
      await Moazzen.audio.play();
    } catch (exc) {
      // console.log({ msg: 'Playing Azan Failed', exc });
    }
  }

  static async stopAzan() {
    try {
      Moazzen.lastTime = Moazzen.audio?.currentTime ?? 0;
      Moazzen.audio?.pause();
      Moazzen.pausedAt = Date.now();
    } catch (exc) {
      // console.log({ msg: 'Stopping the Azan Failed', exc, audio });
    }
  }
}
