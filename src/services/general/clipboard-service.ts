import TavoosService from '../service.ts';

interface ClipboardContent {
  content: any;
  addedAt: Date;
}

export default class ClipboardService extends TavoosService {
  static #content: ClipboardContent[] = [];

  private constructor() {
    super();
  }

  static #createDeepCopy(item: any) {
    return JSON.parse(JSON.stringify(item));
  }

  static copy(content: any) {
    const stringifiedContent = typeof content === 'string' ? content : JSON.stringify(content);

    this.#content = this.#content.filter(item => item.content !== stringifiedContent);

    this.#content.push({
      content: this.#createDeepCopy(content),
      addedAt: new Date(),
    });

    navigator.clipboard.writeText(stringifiedContent);
  }

  static get content(): any[] {
    return this.#content.map((item: any) => this.#createDeepCopy(item));
  }
}
