import TavoosService from '../service.ts';

export default class HistoryService extends TavoosService {
  private constructor() {
    super();
  }

  /**
   * Clears all application history and user preferences from localStorage
   */
  static clearHistory() {
    // List of localStorage keys to clear
    const keysToRemove = [
      // Navigation and bookmarks
      'pageNumber',
      'bookmarks',

      // User preferences
      'reciterSettings',
      'azan-settings',
      'applySummerTime',

      // Location preferences
      'continent',
      'country',
      'city',
    ];

    // Remove each key from localStorage
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });

    console.log('History cleared successfully');
    return true;
  }

  /**
   * Clears all localStorage data (complete reset)
   */
  static clearAllLocalStorage() {
    localStorage.clear();
    console.log('All localStorage data cleared');
    return true;
  }
}
