import { devMode } from '@/utils/utils.ts';
import { useCityStore } from '@/stores/CityStore.ts';
import { useContinentStore } from '@/stores/ContinentStore.ts';
import { useCountryStore } from '@/stores/CountryStore.ts';
import { usePrayersScheduleStore } from '@/stores/pray-times/PrayersScheduleStore.ts';
import { useProcessStore } from '@/stores/ProcessStore.ts';
import City from '@/models/City.ts';
import Country from '@/models/Country.ts';
import Process from '@/models/application/Process.ts';
import Progress from '@/models/application/Progress.ts';
import TavoosService from '@/services/service.ts';
import type Continent from '@/models/Continent.ts';
import { PrayersSchedule } from '@/types.ts';

const PraytimesDBVersion = 3;
const DB_NAME = 'PrayTimeDatabase';
const processStore = useProcessStore();

export function api() {
  return devMode()
    ? 'http://localhost/api/v1/tavoos/pray-times-v2'
    : 'https://behnegar.app/api/v1/tavoos/pray-times-v2';
}

function objectStore(type: string): IDBObjectStore {
  try {
    return PrayTimesDBService.db.transaction([type], 'readwrite').objectStore(type);
  } catch (exc) {
    console.log(`Error while getting objectStore ${type}.`, exc);

    throw exc;
  }
}

function citiesObjectStore() {
  return objectStore('cities');
}
function countriesObjectStore() {
  return objectStore('countries');
}
function schedulesObjectStore() {
  return objectStore('schedules');
}

function dbRequest() {
  return window.indexedDB.open(DB_NAME, PraytimesDBVersion);
}

function lastVisitedContinent() {
  const continentId = parseInt(localStorage.getItem('continent') ?? '4');

  return useContinentStore().continents.find(({ id }: { id: number }) => id == continentId);
}

async function fetchCountriesCount(continent: Continent | undefined): Promise<number | null> {
  try {
    // eslint-disable-next-line prettier/prettier
    const url = continent
      ? `${api()}/continents/${continent.id}/countries/count`
      : `${api()}/countries/count`;

    const response = await fetch(url, {
      headers: {
        accept: 'application/json, text/plain, */*',
      },
    });

    return await response.json();
  } catch (exc) {
    return null;
  }
}

async function fetchCountriesList(continent: Continent | undefined): Promise<Country[]> {
  try {
    // eslint-disable-next-line prettier/prettier
    const url = continent
      ? `${api()}/continents/${continent.id}/countries`
      : `${api()}/countries`;

    const response = await fetch(url, {
      headers: {
        accept: 'application/json, text/plain, */*',
      },
    });

    if (response.ok) return await response.json();
    else throw new Error('Could not download the countries list.');
  } catch (exc) {
    console.log(exc);

    return [] as Country[];
  }
}

function populateCountries() {
  const req1: IDBRequest = countriesObjectStore().getAll();

  req1.onsuccess = async (event: any) => {
    const continent = lastVisitedContinent();

    const expectedCount = await fetchCountriesCount(continent as any);

    const countryRecords = event.target.result.filter(
      (record: { continentId: number }) => record.continentId == continent?.id,
    );

    const lastCountryId = localStorage.getItem('country') ? parseInt(localStorage.getItem('country')!) : undefined;

    if (expectedCount === null) {
      // - The server is not available

      if (countryRecords.length === 0) {
        // The browser does not have any data
        alert('Apparetnly, there is an issue on the server side. Would you please try agian in a while? Sorry for the inconvenience.');
        return;
      }

      if (countryRecords.length === expectedCount) {
        // We can use the local data
        countryRecords
          .map((record: any) => convertSnakeToCamel(record))
          .map((record: any) => new Country(record))
          .forEach((country: Country) => {
            useCountryStore().addCountry(country);
            if (country.id === lastCountryId) queryCitiesOfCountry(country);
          });

        return;
      }
    }

    countriesObjectStore().clear();
    citiesObjectStore().clear();

    try {
      const countriesData = await fetchCountriesList(continent as any);

      countriesData
        .map(record => convertSnakeToCamel(record))
        .map(record => {
          const country = new Country(record as any);
          useCountryStore().addCountry(country);
          if (country.id === lastCountryId) queryCitiesOfCountry(country);
          return record;
        })
        .forEach(record => {
          const req2 = countriesObjectStore().put(record);
          req2.onsuccess = () => (req2.onsuccess = null);
        });
    } catch (exc) {
      console.log({ exc });
    } finally {
      req1.onsuccess = null;
    }
  };
}

function convertSnakeToCamel(obj: Record<string, any>): Record<string, any> {
  const newObj: Record<string, any> = {};

  for (const key in obj) {
    if (!obj.hasOwnProperty(key)) continue;

    const camelKey = key.replace(/_([a-z])/g, (match, p1) => p1.toUpperCase());
    newObj[camelKey] = obj[key];
  }

  return newObj;
}

async function fetchCitiesOfCountryCount(country: Country): Promise<number | null> {
  try {
    // eslint-disable-next-line prettier/prettier
    const url = `${api()}/countries/${country.id}/cities/count`;

    const response = await fetch(url, {
      headers: {
        accept: 'application/json, text/plain, */*',
      },
    });

    return await response.json();
  } catch (exc) {
    return null;
  }
}
async function fetchCitiesOfCountry(country: Country) {
  const process: Process = new Process(`Downloading cities of ${country.name}`, country);
  const updater = setInterval(() => process.progress.increment(1), 250);

  try {
    const response = await fetch(`${api()}/countries/${country.id}/cities`, {
      headers: {
        accept: 'application/json, text/plain, */*',
      },
    });

    const cities = (await response.json()).map((record: Record<string, any>) => convertSnakeToCamel(record));

    return cities;
  } finally {
    process.complete();
    clearInterval(updater);
  }
}

function queryCitiesOfCountry(country: Country) {
  const request: IDBRequest = citiesObjectStore().getAll();

  request.addEventListener('success', async (event: any) => {
    if (
      processStore.processes.some((p: Process) => p.client === country && p.name === `Preparing cities of ${country.name}`)
    ) {
      return;
    }

    const expectedCount = await fetchCitiesOfCountryCount(country);

    const progress = new Progress(0, expectedCount ?? 0);
    const process: Process = new Process(`Preparing cities of ${country.name}`, country, progress);

    const cityRecords = event.target.result.filter(({ countryId }: { countryId: number }) => countryId == country.id);

    if (expectedCount === cityRecords.length || expectedCount === null) {
      cityRecords
        .map((record: Record<string, any>) => convertSnakeToCamel(record))
        .map((record: Record<string, any>) => new City(record as any))
        .forEach((city: City) => useCityStore().addCity(city));

      process.complete();
      return;
    }

    await populateCitiesOfCountry(country);

    process.complete();
  });
}

async function populateCitiesOfCountry(country: Country) {
  try {
    const citiesData = await fetchCitiesOfCountry(country);

    citiesData
      .map((record: Record<string, any>) => convertSnakeToCamel(record))
      .map((record: Record<string, any>) => {
        useCityStore().addCity(new City(record as any));
        return record;
      })
      .forEach((record: Record<string, any>) => {
        const req = citiesObjectStore().put(record);
        req.onsuccess = () => (req.onsuccess = null);
      });
  } catch (exc) {
    console.log({ exc });
  }
}

function querySchedule(city: City, date: Date) {
  return new Promise((resolve, reject) => {
    const getRequest = schedulesObjectStore().get(`${date.getFullYear()}:${city.id}`);
    getRequest.onsuccess = () => {
      try {
        resolve(getRequest.result.schedule);
      } catch (err) {
        reject(err);
      }
    };
  });
}

async function downloadSchedule(city: City, date: Date) {
  const response = await fetch(`${api()}/cities/${city.id}/schedule`, {
    headers: {
      accept: 'application/json, text/plain, */*',
    },
  });

  if (!response.ok) {
    throw `${response.status} ${response.statusText}`;
  }

  const rawSchedule = await response.json();
  const schedule = rawSchedule.schedule ?? rawSchedule;

  const schedulesStore = PrayTimesDBService.db.transaction(['schedules'], 'readwrite').objectStore('schedules');
  schedulesStore.put({
    id: `${date.getFullYear()}:${city.id}`,
    schedule,
  });

  return schedule;
}

function prayDate(date: Date, timeString: string | undefined): Date | null {
  if (!timeString) return null;

  const [hour, minute] = timeString
    .replace(/ /g, '')
    .split(':')
    .map(x => Number(x));

  return new Date(new Date(date).setHours(hour, minute));
}

function openDB(callback: Function) {
  if (PrayTimesDBService.db) {
    callback?.();
    return;
  }

  const request = dbRequest();

  request.onblocked = event => console.log(event);

  request.addEventListener('versionchange', (event: any) => {
    console.log('versionchange event disptched', event);
  });

  request.addEventListener('error', event => {
    const errorCode = (event.target as any)?.errorCode;

    if (errorCode !== undefined) {
      window.console.log(`Database error: ${errorCode}`);
      return;
    }

    window.indexedDB.databases()
      .then(databases => {
        const names = databases.map(database => database.name);
        if (!names.includes(DB_NAME)) {
          /// The problematic case
          return;
        }

        window.indexedDB.deleteDatabase(DB_NAME);

        openDB(callback);
      })
      .catch ((exc: DOMException) => {
        if (exc.message === 'Internal error.') {
          alert('You need to restart your browser due to some internal issue. Probably you have recently experienced a shortage of disk space.');
        } else {
          alert('Something went wrong. We recommend you to restart your browser.');
        }
      });
  });

  request.addEventListener('success', event => {
    PrayTimesDBService.db = (event.target as IDBOpenDBRequest).result;
    callback?.();
  });

  request.addEventListener('upgradeneeded', (event: IDBVersionChangeEvent) => {
    const database = (event.target as IDBOpenDBRequest).result as IDBDatabase;

    ['cities', 'countries', 'schedules'].forEach((storeName: string) => {
      try {
        database.deleteObjectStore(storeName);
      } catch (err) {
        // TODO: Take appropriate action on failure
      }

      try {
        database.createObjectStore(storeName, { keyPath: 'id' });
      } catch (exc) {
        console.log('Error while upgrading praytimes database', { exc });
      }
    });
  });
}

export default class PrayTimesDBService extends TavoosService {
  static db: IDBDatabase;

  static inflate() {
    openDB(populateCountries);
  }

  static getCitiesOfCountry(country: Country) {
    queryCitiesOfCountry(country);
  }

  static async getDailyScheduleForCity(city: City, date = new Date()) {
    let rawSchedule;

    try {
      rawSchedule = await querySchedule(city, date);
    } catch {
      try {
        rawSchedule = await downloadSchedule(city, date);
      } catch (err2) {
        console.log({ error: err2 });
        return;
      }
    }

    const start = new Date(date.getFullYear(), 0, 0);
    const timeDifference = date.getTime() - start.getTime();
    const timezoneDifference = (start.getTimezoneOffset() - date.getTimezoneOffset()) * 60 * 1000;
    const diff = timeDifference + timezoneDifference;
    const oneDay = 1000 * 60 * 60 * 24;
    const day = Math.floor(diff / oneDay);

    const schedule = rawSchedule[day - 1];

    usePrayersScheduleStore().addSchedule({
      city,
      date: new Date(schedule.day),
      timeTable: {
        emsak:    prayDate(new Date(schedule.day), schedule?.emsak),
        fajr:     prayDate(new Date(schedule.day), schedule?.fajr),
        sunrise:  prayDate(new Date(schedule.day), schedule?.sunrise),
        dhohr:    prayDate(new Date(schedule.day), schedule?.dhohr),
        aser:     prayDate(new Date(schedule.day), schedule?.aser),
        sunset:   prayDate(new Date(schedule.day), schedule?.sunset),
        maghreb:  prayDate(new Date(schedule.day), schedule?.maghreb),
        isha:     prayDate(new Date(schedule.day), schedule?.isha),
        midnight: prayDate(new Date(schedule.day), schedule?.midnight),
      },
    });
  }

  static async getYearlyScheduleForCity(city: City, date = new Date()) {
    let rawSchedule;

    try {
      rawSchedule = await querySchedule(city, date);
    } catch {
      try {
        rawSchedule = await downloadSchedule(city, date);
      } catch (err2) {
        console.log({ error: err2 });
        return;
      }
    }

    const bakedSchedules = rawSchedule.map((dailySchedule: any) => ({
      city,
      date: new Date(dailySchedule.day),
      timeTable: {
        emsak:    prayDate(new Date(dailySchedule.day), dailySchedule.emsak),
        fajr:     prayDate(new Date(dailySchedule.day), dailySchedule.fajr),
        sunrise:  prayDate(new Date(dailySchedule.day), dailySchedule.sunrise),
        dhohr:    prayDate(new Date(dailySchedule.day), dailySchedule.dhohr),
        aser:     prayDate(new Date(dailySchedule.day), dailySchedule.aser),
        sunset:   prayDate(new Date(dailySchedule.day), dailySchedule.sunset),
        maghreb:  prayDate(new Date(dailySchedule.day), dailySchedule.maghreb),
        isha:     prayDate(new Date(dailySchedule.day), dailySchedule.isha),
        midnight: prayDate(new Date(dailySchedule.day), dailySchedule.midnight),
      },
    }));

    bakedSchedules.forEach((schedule: PrayersSchedule) => usePrayersScheduleStore().addSchedule(schedule));
  }
}
