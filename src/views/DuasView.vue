<script setup lang="ts">
import Api from '@/services/api/api.ts';
import { Ref, onMounted, ref } from 'vue';
import { RouterView, useRouter } from 'vue-router';

interface DuaListItem {
  slug: string;
  name: {
    arabic: string;
  };
}

const router = useRouter();

const duasList: Ref<DuaListItem[]> = ref([]);
const currentDua: Ref<DuaListItem | undefined> = ref();

async function fetchList() {
  const url = new Api().duaServiceURL;

  try {
    const result = await fetch(url + '/');
    if (result.status !== 200) {
      console.log(`Got ${result.status} response!`, { result });

      return;
    }

    duasList.value = await result.json();
  } catch (exc) {
    console.log('Error while getting Duas list', { exc });
  }
}

function updateCurrentDua(state: string, dua: DuaListItem) {
  if (state === 'open') {
    currentDua.value = dua;
    router.push({
      name: 'duaShow',
      params: { slug: dua.slug },
    });
  }
}

onMounted(fetchList);
</script>

<template>
  <div class="h-full space-y-1 overflow-auto rtl relative">
    <details
      class="rounded"
      v-for="(item, index) in duasList"
      :key="item.slug"
      name="dua"
      :data-id="`dua-${index}`"
      @toggle="(event: ToggleEvent) => updateCurrentDua(event.newState, item)"
    >
      <summary class="secondary arabic title p-2 tab-title md:p-6 text-lg sticky top-0">
        <span class="ms-2">{{ item.name.arabic }}</span>
      </summary>

      <RouterView v-if="item == currentDua" />
    </details>
  </div>
</template>

<style scoped>
li:has(.router-link-active.router-link-exact-active) {
  background-color: var(--color-primary-background-color);
  color: var(--color-primary-text-color);
}
li .router-link-active.router-link-exact-active {
  background: transparent;
  color: var(--color-primary-text-color);
}
</style>
