<script setup lang="ts">
import Api from '@/services/api/api.ts';
import { Ref, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import ArticleBox from '@/components/ArticleBox.vue';
import { watch } from 'vue';

const route = useRoute();

const arabicParagraphs: Ref<string[]> = ref([]);
const persianParagraphs: Ref<string[]> = ref([]);

async function fetchArabic() {
  const { slug } = route.params;
  const url = `${new Api().duaServiceURL}/${slug}/arabic`;

  try {
    const result = await fetch(url);
    if (result.status !== 200) return;

    const text = await result.text();

    arabicParagraphs.value = text
      .replace(/^"(.*)"$/, '$1')
      .split('\\n')
      .map(x => x.trim())
      .filter(x => x);
  } catch (exc) {
    console.log('Error while getting Hadith');
  }
}

async function fetchPersian() {
  const { slug } = route.params;
  const url = `${new Api().duaServiceURL}/${slug}/persian`;

  try {
    const result = await fetch(url);
    if (result.status !== 200) return;

    const text = await result.text();

    persianParagraphs.value = text
      .replace(/^"(.*)"$/, '$1')
      .split('\\n')
      .map(x => x.trim())
      .filter(x => x);
  } catch (exc) {
    console.log('Error while getting Hadith');
  }
}

async function fetchDua() {
  await fetchArabic();
  await fetchPersian();
}

watch(() => route.params.slug, fetchDua);

onMounted(fetchDua);
</script>

<template>
  <ArticleBox
    :paragraphCollections="[arabicParagraphs, persianParagraphs]"
    class="primary"
  />
</template>
