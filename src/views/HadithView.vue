<script setup lang="ts">
import Api from '@/services/api/api.ts';
import ArticleBox from '@/components/ArticleBox.vue';
import { onMounted, ref } from 'vue';

let content = ref(['']);

async function fetchHadith() {
  const url = new Api().ahadithServiceURL;

  try {
    const result = await fetch(url);
    const text = await result.text();

    content.value = text
      .replace(/^"(.*)"$/, '$1')
      .split('\\n')
      .map(x => x.trim())
      .filter(x => x);
  } catch (exc) {
    console.log('Error while getting Hadith');
  }
}

onMounted(fetchHadith);
</script>

<template>
  <div class="h-full p-2">
    <ArticleBox
      title="خطبه فدکیه"
      :paragraphCollections="[[], content]"
      content-language="farsi"
    />
  </div>
</template>
