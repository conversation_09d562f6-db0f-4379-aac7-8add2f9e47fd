import { createRouter, createWebHistory } from 'vue-router';
import HomeView from '../views/HomeView.vue';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/duas',
      name: 'duas',
      component: () => import('../views/DuasView.vue'),
      children: [
        {
          path: ':slug/view',
          name: 'duaShow',
          component: () => import('../views/DuasShowView.vue'),
        },
      ],
    },
    {
      path: '/hadith',
      name: 'hadith',
      component: () => import('../views/HadithView.vue'),
    },
    {
      path: '/mobile-app',
      name: 'mobileApp',
      component: () => import(/* webpackChunkName: "MobileApp" */ '../views/MobileAppView.vue'),
    },
    {
      path: '/praytimes',
      name: 'praytimes',
      component: () => import('../views/PrayTimesView.vue'),
    },
    {
      path: '/quran/:pageNumber?',
      name: 'quran',
      component: () => import('../views/QuranView.vue'),
    },
    {
      path: '/social-media',
      name: 'socialMedia',
      component: () => import(/* webpackChunkName: "MobileApp" */ '../views/SocialMediaView.vue'),
    },
  ],
});

export default router;
