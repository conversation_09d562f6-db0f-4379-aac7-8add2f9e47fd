<script setup lang="ts">
import { onUnmounted, ref } from 'vue';

// Services
import Reciter from '@/services/quran/reciter-service.ts';

// Icons
import HeadphoneIcon from '@/components/icons/HeadphoneIcon.vue';
import MediaPauseIcon from '@/components/icons/MediaPauseIcon.vue';
import MediaPlayIcon from '@/components/icons/MediaPlayIcon.vue';
import SettingsIcon from '@/components/icons/SettingsIcon.vue';

// Components
import CircularProgressbar from '@/components/ui/CircularProgressbar.vue';
import IconButton from '@/components/ui/IconButton.vue';

defineEmits(['reciter-settings-request']);

const expanded = ref(false);
const reciter: Reciter = Reciter.instance;

onUnmounted(() => reciter.flush());
</script>

<template>
  <div
    class="flex landscape:flex-col items-center gap-2 rounded-full transition-all max-w-fit"
    :class="[expanded ? 'primary expanded' : 'w-12 h-12 overflow-hidden']"
  >
    <CircularProgressbar
      class="w-12 bg-inherit cursor-pointer"
      :color="expanded ? 'black' : 'white'"
      :percentage="Math.floor(100 * reciter.progress.value)"
      @click.stop="() => (expanded = !expanded)"
    >
      <IconButton
        class="w-12 shrink-0 circular-progressbar"
        :class="{ shadowed: expanded }"
        :style="[expanded && 'background: inherit', 'padding: 0.25rem']"
      >
        <HeadphoneIcon />
      </IconButton>
    </CircularProgressbar>

    <!-- <div v-if="reciter.progress.value > 0.5">{{ reciter.progress }}</div> -->

    <IconButton
      @click.stop="() => reciter.playOrPause()"
      class="w-12 shrink-0"
    >
      <MediaPauseIcon v-if="reciter.state.value === 'playing'" />
      <MediaPlayIcon v-else />
    </IconButton>

    <IconButton
      class="w-11 shrink-0"
      @click="$emit('reciter-settings-request')"
    >
      <SettingsIcon />
    </IconButton>
  </div>
</template>

<style scoped>
@tailwind base;

@layer base {
  .shadowed {
    filter:
      drop-shadow(8px  0px  8px rgba(0, 128, 128, 0.5))
      drop-shadow(-8px  0px 8px rgba(0, 128, 128, 0.5))
      drop-shadow( 0px  8px 8px rgba(0, 128, 128, 0.5))
      drop-shadow( 0px -8px 8px rgba(0, 128, 128, 0.5));
  }
}

/* Hide time display of the audio element */
audio::-webkit-media-controls-time-remaining-display,
audio::-webkit-media-controls-current-time-display {
  display: none !important;
}

.expanded {
  @media screen and (orientation: portrait) {
    padding-inline-end: 0.5rem;
  }

  @media screen and (orientation: landscape) {
    padding-block-end: 0.5rem;
  }
}
</style>
