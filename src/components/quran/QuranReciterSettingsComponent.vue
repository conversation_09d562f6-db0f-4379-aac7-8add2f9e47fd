<script setup lang="ts">
import { ref, watch, watchEffect } from 'vue';

// Types
import { useChapterStore } from '@/stores/quran/chapter-store.ts';
import { useVerseStore } from '@/stores/quran/verse-store.ts';
import { IChapter, IVerse } from '@/types/types-quran.ts';

// Utilities
import NumberUtils from '@/utils/number-utils.ts';

// Services
import Reciter from '@/services/quran/reciter-service.ts';

type ReciterRegion = 'this-page' | 'entire-quran' | 'custom';

const chaptersStore = useChapterStore();
const versesStore = useVerseStore();

const reciter: Reciter = Reciter.instance;

const region = ref<ReciterRegion>(reciter.settings.region);
const readNumbers = ref(reciter.settings.readNumbers);

const startVerse = ref<IVerse>(findVerse(reciter.settings.startVerse) ?? versesStore.verses[0]);
const endVerse = ref<IVerse>(findVerse(reciter.settings.endVerse) ?? versesStore.verses.at(-1)!);

const startChapter = ref<IChapter>(startVerse.value.chapter);
const endChapter = ref<IChapter>(endVerse.value.chapter);

function findVerse(indexInQuran: number) {
  return versesStore.verses.find(verse => verse.indexInQuran === indexInQuran);
}

watch(() => startChapter.value, () => {
  if (endChapter.value.index < startChapter.value.index) {
    endChapter.value = chaptersStore.chapters.at(-1)!;
  }

  if (!startChapter.value.verses.includes(startVerse.value)) {
    startVerse.value = startChapter.value.verses[0];
  }
});

watch(() => endChapter.value, () => {
  if (!endChapter.value.verses.includes(endVerse.value)) {
    endVerse.value = endChapter.value.verses.at(-1)!;
  }
});

watch(() => startVerse.value, () => {
  if (endVerse.value.indexInQuran < startVerse.value.indexInQuran) {
    endVerse.value = endChapter.value.verses.at(-1)!;
  }
})

watchEffect(() => {
  reciter.settings = {
    readNumbers: readNumbers.value,
    region: region.value,
    startVerse: startVerse.value.indexInQuran,
    endVerse: endVerse.value.indexInQuran,
  };
});
</script>

<template>
  <div class="farsi p-4 space-y-4">
    <label class="primary p-2 rounded">
      <input
        type="checkbox"
        v-model="readNumbers"
      />
      <span>شماره آیات را بخوان</span>
    </label>

    <fieldset>
      <legend>محدوده تکرار</legend>
      <div class="ps-4">
        <label>
          <input type="radio" name="repeat-region" v-model="region" value="this-page" />
          <span>همین صفحه</span>
        </label>
        <label>
          <input type="radio" name="repeat-region" v-model="region" value="entire-quran" />
          <span>کل قرآن</span>
        </label>
        <label>
          <input type="radio" name="repeat-region" v-model="region" value="custom" />
          <span>محدوده سفارشی</span>
        </label>

        <div
          v-if="region === 'custom'"
          class="p-4 space-y-4"
        >
          <fieldset class="flex gap-4">
            <legend>از</legend>

            <select
              class="primary flex-1"
              v-model="startChapter"
            >
              <option
                v-for="chapter in chaptersStore.chapters"
                :key="chapter.title + ' ' + chapter.index"
                :value="chapter"
              >
                {{ chapter.title }}
              </option>
            </select>

            <select
              v-if="startChapter"
              class="primary"
              v-model="startVerse"
            >
              <option
                v-for="verse in startChapter.verses"
                :key="startChapter.title + ' ' + startChapter.index + ' ' + verse.indexInQuran"
                :value="verse"
              >
                {{ NumberUtils.persianize(verse.index) }}
              </option>
            </select>
          </fieldset>

          <fieldset class="flex gap-4">
            <legend>تا</legend>

            <select
              class="primary flex-1"
              v-model="endChapter"
            >
              <option
                v-for="chapter in chaptersStore.chapters.filter(({ index }) => index >= startChapter.index)"
                :key="chapter.title + ' ' + chapter.index"
                :value="chapter"
              >
                {{ chapter.title }}
              </option>
            </select>

            <select
              v-if="endChapter"
              class="primary"
              v-model="endVerse"
            >
              <option
                v-for="verse in endChapter.verses.filter(v => v.indexInQuran >= startVerse.indexInQuran)"
                :key="endChapter.title + ' ' + endChapter.index + ' ' + verse.indexInQuran"
                :value="verse"
              >
                {{ NumberUtils.persianize(verse.index) }}
              </option>
            </select>
          </fieldset>
        </div>
      </div>
    </fieldset>
  </div>
</template>

<style scoped>
input[type='checkbox'],
input[type='radio'] {
  aspect-ratio: 1;
  @apply w-6;
}

label {
  @apply flex gap-2 items-center;

  * {
    @apply p-2;
  }
}

legend {
  @apply mb-4;
}

select {
  @apply h-8 w-28;
}
</style>
