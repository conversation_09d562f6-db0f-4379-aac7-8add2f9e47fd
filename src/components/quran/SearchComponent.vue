<script setup lang="ts">
// Vue stuff
import { computed, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

// Types
import { IVerse } from '@/types/types-quran.ts';

// Utilities
import BrowserUtils from '@/utils/browser-utils.ts';
import NumberUtils from '@/utils/number-utils.ts';
import StringUtils from '@/utils/string-utils';
import VerseUtils from '@/utils/verse-utils';

// Pinia Stores
import { useVerseStore } from '@/stores/quran/verse-store.ts';

type searchType = 'containment' | 'fuzzy' | 'full';

const emit = defineEmits(['search-result-clicked']);

const router = useRouter();

// Pinia Stores
const verseStore = useVerseStore();

const resultsList = ref<HTMLUListElement>();

interface SearchResult {
  verse: IVerse;
  matches: RegExpExecArray[];
}

const numberOfResultsToRender = ref(50);
const searchResults = ref<SearchResult[]>([]);
const searchTerm = ref('');
const searchTimeout = ref<NodeJS.Timeout>();

const searchFuzzy = ref(true);

const searchType = computed(() => searchFuzzy.value ? 'fuzzy' : 'containment' as searchType);

watch([searchTerm, searchType], ([key, type]) => {
  numberOfResultsToRender.value = 50;

  if (key.trim() === '') return;

  clearTimeout(searchTimeout.value);
  searchTimeout.value = setTimeout(() => searchOffline({ key }), 350);
});

const renderableResults = computed(() => searchResults.value.slice(0, numberOfResultsToRender.value));

function updateList(event: UIEvent) {
  const element = event.currentTarget! as HTMLUListElement;
  const closeToScrollEnd = element.scrollTop + element.offsetHeight >= element.scrollHeight * 0.8;

  numberOfResultsToRender.value += closeToScrollEnd ? 50 : 0;
}

function onSearchResultClick(verse: IVerse) {
  const page = verse.page;

  if (!page) {
    console.error('Page not found for IVerse', verse);

    return;
  }

  router.push({
    path: `/quran/${page.index}`,
    query: {
      highlight: verse.indexInQuran.toString(),
    },
  });

  emit('search-result-clicked');
}

function decorate(result: SearchResult): string {
  return searchType.value === 'fuzzy'
    ? decorateFuzzy(result)
    : decoratContainment(result);
}

function decoratContainment(result: SearchResult): string {
  const words = result.verse.text.split(/\s+/);
  const purifiedWords = result.verse.purifiedText.split(/\s+/);

  return words.map((word, index) => {
      return result.matches.flat().some(match => purifiedWords[index].includes(match))
        ? `<span class="text-highlight">${word}</span>`
        : word;
    })
    .join(' ');
}

function decorateFuzzy(result: SearchResult): string {
  const words = result.verse.text.split(/\s+/);
  const purifiedWords = result.verse.purifiedText.split(/\s+/);

  return words.map((word, index) => {
      return result.matches.flat().some(match => purifiedWords[index].includes(match))
        ? `<span class="text-highlight">${word}</span>`
        : word;
    })
    .join(' ');
}

async function searchOffline({ key }: { key: string; }) {
  const verses: IVerse[] = Object.values(verseStore.verses);

  const stringUtils = new StringUtils();
  const purifiedKey = key.split(/\s+/).map(word => new StringUtils().purify(word)).join(' ');
  const zippedPurifiedKey = stringUtils.zip(purifiedKey);

  let regExp: RegExp;
  switch(searchType.value) {
    case 'full':
      regExp = new RegExp(` ${zippedPurifiedKey} `, 'g');
      break;
    case 'containment':
      regExp = new RegExp(purifiedKey, 'g');
      break;
    case 'fuzzy':
      regExp = new RegExp([...purifiedKey].join('\\S*'), 'g');
      break;
    default:
      regExp = new RegExp('', 'g');
      break;
  }

  searchResults.value = verses
    .map(verse => ({
      verse,
      matches: [...verse.purifiedText.matchAll(regExp)],
    }) as SearchResult)
    .filter(result => result.matches.length > 0);
}
</script>

<template>
  <div class="h-full overflow-auto flex flex-col gap-2 p-2 md:p-4">
    <ul
      ref="resultsList"
      class="flex-1 overflow-auto"
      @scroll="updateList"
    >
      <li
        v-for="result in renderableResults"
        :key="`search-result-${result.verse.indexInQuran}`"
        class="px-2 md:ps-4 py-2 flex items-center gap-2 arabic text-start"
        @click.stop="() => onSearchResultClick(result.verse)"
      >
        <div
          class="flex-1 whitespace-nowrap text-ellipsis overflow-hidden text-xl"
          v-html="decorate(result)"
          style="line-height: normal; font-family: me-quran;"
        >
        </div>

        <div
          class="min-w-24 h-fit flex items-center gap-2 justify-between primary rounded-full px-3"
          style="line-height: normal"
        >
          <span class="whitespace-nowrap">{{ result.verse.chapter.title }}</span>
          <span>{{ NumberUtils.persianize(result.verse.index) }}</span>
        </div>
      </li>
    </ul>

    <label class="flex gap-2 md:gap-4 items-center rtl">
      <input
        type="checkbox"
        v-model="searchFuzzy"
        class="p-1 md:p-4 rounded"
      >
      <!-- <span>Fuzzy</span> -->
      <span class="text-sm md:text-xl">تقریبی</span>
    </label>

    <div class="flex items-center gap-4 px-2 border-t-2 pt-2">
      <div
        class="my-2 farsi shrink-0"
        v-if="searchResults.length"
      >
        {{ NumberUtils.persianize(searchResults.length) }} نتیجه
      </div>

      <div class="flex-1">
        <input
          class="primary px-2 py-3 rounded-lg w-full farsi"
          placeholder="پی چی می‌گردی؟"
          type="text"
          v-model="searchTerm"
        />
      </div>
    </div>
  </div>
</template>
