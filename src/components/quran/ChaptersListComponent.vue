<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { IChapter } from '@/types/types-quran.ts';
import NumberUtils from '@/utils/number-utils.ts';
import { useChapterStore } from '@/stores/quran/chapter-store.ts';
import StringUtils from '@/utils/string-utils.ts';

const props = defineProps<{
  isBusy: boolean;
  selectedChapter: IChapter | undefined;
}>();

defineEmits(['select']);

const chapterStore = useChapterStore();

const searchTerm = ref('');

const filteredChapters = computed(() => {
  if (!searchTerm.value) return chapterStore.chapters;

  const persianizedSearchTerm = new StringUtils().persianize(searchTerm.value);
  return chapterStore.chapters.filter(chapter => new StringUtils().persianize(chapter.title).includes(persianizedSearchTerm));
});
const firstColumnChapters = computed(() => filteredChapters.value.filter((_, index) => index % 2 === 0));
const secondColumnChapters = computed(() => filteredChapters.value.filter((_, index) => index % 2 === 1));

function scrollIntoView() {
  const item = document.querySelector(`#chapter-list-item-${props.selectedChapter?.index}`);

  item?.scrollIntoView({
    behavior: 'smooth',
    block: 'center',
  });
}

onMounted(() => scrollIntoView());
</script>

<template>
  <div class="h-full overflow-hidden flex flex-col">
    <div
      class="flex-1 overflow-auto grid grid-cols-2 gap-2 md:gap-8 rtl"
      :class="{ 'blur-sm pointer-events-none': isBusy }"
    >
      <ol
        v-for="(columnChapters, index) in [firstColumnChapters, secondColumnChapters]"
        :key="`Chapters list half ${index + 1}`"
        class="h-full"
      >
        <li
          v-for="chapter in columnChapters"
          :key="`Chapter list item ${chapter.index} ${chapter.title}`"
          :id="`chapter-list-item-${chapter.index}`"
          class="p-4 whitespace-nowrap"
          :class="{ primary: chapter.index === props.selectedChapter?.index }"
          @click.stop="$emit('select', chapter)"
        >
          <div class="flex gap-2 md:gap-4">
            <span
              class="w-8 text-start shrink-0"
              style="direction: ltr"
            >
              {{ NumberUtils.persianize(chapter.index) }}
            </span>
            <span class="flex-1">{{ chapter.title }}</span>
          </div>
        </li>
      </ol>
    </div>
    <div class="p-2 secondary">
      <input
        type="text"
        name=""
        id=""
        class="w-full p-4 rounded-full primary rtl"
        placeholder="جستجو ..."
        @input="input => (searchTerm = (input.currentTarget as HTMLInputElement).value)"
      />
    </div>
  </div>
</template>
