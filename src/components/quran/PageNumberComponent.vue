<script setup lang="ts">
import { ref } from 'vue';
import NumberUtils from '@/utils/number-utils.ts';

defineProps<{ value: number }>();

const emit = defineEmits(['change']);

const currentValue = ref('');

function emitChangeEvent(event: Event) {
  const div = event.currentTarget as HTMLDivElement;

  emit('change', parseInt(div.innerText));
}

function checkPageNumber(event: KeyboardEvent) {
  const navigationKeys = ['ArrowLeft', 'ArrowRight', 'Backspace', 'Tab'];

  if (navigationKeys.includes(event.key)) return;

  if (event.key === 'Escape') {
    const div = event.currentTarget as HTMLDivElement;
    div.innerHTML = currentValue.value;
    div.blur();

    currentValue.value = '';
  }

  if (isNaN(parseInt(event.key))) {
    event.preventDefault();
  }
}

function memorizeCurrentValue(e: Event) {
  const div = e.currentTarget as HTMLDivElement;

  currentValue.value = div.innerHTML;
}
</script>

<template>
  <div
    class="text-xl font-bold hover:outline outline-1"
    contenteditable="true"
    @keyup.enter="event => (event.currentTarget as HTMLDivElement).blur()"
    @blur.enter="emitChangeEvent"
    @focus="memorizeCurrentValue"
    @keydown="checkPageNumber"
  >
    {{ NumberUtils.persianize(value) }}
  </div>
</template>
