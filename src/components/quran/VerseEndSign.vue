<script setup lang="ts">
import { computed, ref } from 'vue';

// Types
import { MenuItem, IVerse } from '@/types/types-quran.ts';
import { Side } from '@/types/types-general.ts';

// Services
import ClipboardService from '@/services/general/clipboard-service.ts';
import Reciter from '@/services/quran/reciter-service.ts';

// Icons
import AcademyCapIcon from '@/components/icons/AcademyCapIcon.vue';
import ClipboardCheckmarkIcon from '@/components/icons/ClipboardCheckmarkIcon.vue';
import ClipboardPlusIcon from '@/components/icons/ClipboardPlusIcon.vue';
import MediaPauseIcon from '@/components/icons/MediaPauseIcon.vue';
import MediaPlayIcon from '@/components/icons/MediaPlayIcon.vue';
import StickyNoteIcon from '@/components/icons/StickyNoteIcon.vue';
import TranslationIcon from '@/components/icons/TranslationIcon.vue';

// Components
import CircularProgressbar from '@/components/ui/CircularProgressbar.vue';
import VerseEndSignMenuItem from '@/components/quran/VerseEndSignMenuItem.vue';

const props = defineProps<{
  verse: IVerse;
  highlight: boolean;
}>();

defineExpose({
  verse: props.verse,
  collapseMenu: deactivateMenu,
});

const reciter = Reciter.instance;
const remoteURL = 'https://tavoos.eu/quran/verse';

const side = ref<Side>('left');
const hasJustBeenCopiedToTheClipboard = ref(false);

const isMenuActivated = ref(false);
const isActiveInReciter = computed(() => reciter.isActive.value && props.verse === reciter.currentVerse.value);

const verseURL = computed(() => `https://tavoos.eu/quran/page/${props.verse.page?.index}?highlight=${props.verse.indexInQuran}`);

const menuItems = computed(() => {
  const isBeingRecited = reciter.currentVerse.value === props.verse && reciter.isPlaying.value;

  const items = [
    {
      icon: TranslationIcon,
      index: 0,
      title: 'ترجمه',
      action: () => navigateTo('translations'),
    },
    {
      icon: StickyNoteIcon,
      index: 1,
      title: 'یادداشت‌ها',
      action: () => navigateTo('notes/list'),
    },
    {
      icon: AcademyCapIcon,
      index: 2,
      title: 'تفسیر',
      action: () => navigateTo('studies'),
    },
    {
      icon: isBeingRecited ? MediaPauseIcon : MediaPlayIcon,
      index: 3,
      title: isBeingRecited ? 'توقف' : 'پخش',
      action: () => reciter.playOrPause(props.verse),
    },
    {
      icon: hasJustBeenCopiedToTheClipboard.value ? ClipboardCheckmarkIcon : ClipboardPlusIcon,
      index: 4,
      title: 'نسخه‌برداری',
      action: () => copyToClipboard(),
      disabled: hasJustBeenCopiedToTheClipboard.value,
    },
  ];

  return items as MenuItem[];
});

function onClick(event: MouseEvent) {
  const element = event.currentTarget as HTMLElement;
  const parent = element.parentElement!;

  const x = element.offsetLeft;
  const y = element.offsetTop;

  const rightOrLeft: Side = x < parent.clientWidth / 2 ? 'right' : 'left';

  const heightRatio = y / parent?.clientHeight;

  if (1 / 5 < heightRatio && heightRatio < 4 / 5) {
    activateMenu(rightOrLeft);
  } else {
    const topOrBottom = heightRatio >= 4 / 5 ? 'top' : 'bottom';

    activateMenu(`${topOrBottom}-${rightOrLeft}`);
  }
}

function activateMenu(onSide: Side = 'left') {
  isMenuActivated.value = true;

  side.value = onSide;

  setTimeout(() => deactivateMenu(), 5000);
}

function deactivateMenu() {
  isMenuActivated.value = false;
}

function navigateTo(destination: string) {
  const url = [remoteURL, props.verse.chapter.index, props.verse.index, destination].join('/');

  window.location.href = url;
}

function copyToClipboard() {
  ClipboardService.copy([props.verse.text, verseURL.value].join('\n'));

  hasJustBeenCopiedToTheClipboard.value = true;

  setTimeout(() => (hasJustBeenCopiedToTheClipboard.value = false), 2000);
}
</script>

<template>
  <div
    class="absolute transition-opacity w-[5%] aspect-square"
    :style="{
      left: `${100 * (props.verse.endSignCoordinates?.x ?? 0)}%`,
      top: `${100 * (props.verse.endSignCoordinates?.y ?? 0)}%`,
    }"
    @click.stop="onClick"
  >
    <CircularProgressbar
      class="w-full -translate-x-1/2 -translate-y-1/2 mix-blend-multiply cursor-pointer hover:bg-tavoos-orange"
      :class="{
        'opacity-100 hover:opacity-100 bg-tavoos-orange': isMenuActivated || isActiveInReciter,
        'bg-tavoos-orange': isMenuActivated,
        'ring-4 ring-offset-8 ring-tavoos-orange': props.highlight,
      }"
      :percentage="reciter.currentVerse.value === props.verse ? Math.floor(100 * reciter.progress.value) : 0"
    />

    <VerseEndSignMenuItem
      v-for="item in menuItems"
      :key="`verse-end-sign-menu-item-at-index-${item.index}`"
      :active="isMenuActivated"
      :item="item"
      :side="side"
      :totalItems="menuItems.length"
      @click.stop="item.action"
      :class="{ 'pointer-events-none': item.disabled }"
    />
  </div>
</template>
