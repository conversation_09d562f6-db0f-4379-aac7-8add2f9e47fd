<script setup lang="ts">
const props = defineProps<{
  width?: number;
  height?: number;
}>();
</script>

<template>
  <svg
    :height="props.height ?? props.width ?? '100%'"
    :width="props.width ?? props.height ?? '100%'"
    class="fill-current"
    viewBox="0 0 16 16"
    version="1.1"
  >
    <path
      fill="current"
      d="M0 4v8h16v-8h-16zM15 11h-14v-4h14v4z"
    ></path>
    <path
      fill="current"
      d="M0 0h16v3h-16v-3z"
    ></path>
    <path
      fill="current"
      d="M0 13h16v3h-16v-3z"
    ></path>
  </svg>
</template>
