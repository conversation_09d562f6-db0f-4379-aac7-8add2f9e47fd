<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  width?: number;
  height?: number;
  strokeWidth?: 'normal' | 'thick';
}>();

const stroke = computed(() => {
  switch (props.strokeWidth) {
    case 'normal':
      return '1.91px';
    case 'thick':
      return '2.11px';
    default:
      throw 'unsupported mode';
  }
});
</script>

<template>
  <!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools -->
  <svg
    :height="props.height ?? props.width ?? '100%'"
    :width="props.width ?? props.height ?? '100%'"
    class="fill-none stroke-current"
    viewBox="0 0 24 24"
    :style="`fill: none; stroke-miterlimit: 10; stroke-width: ${stroke}`"
  >
    <path
      class="cls-1"
      d="M17.07,20.61H9.79a2,2,0,0,1-2-2,2,2,0,0,1,2-2h1.87L5,9.86a2,2,0,0,1-.19-2.65,1.88,1.88,0,0,1,1.47-.68,1.84,1.84,0,0,1,1.35.55l4.06,4.06,4.08-3.06a1.91,1.91,0,0,1,2.5.18h0A17.18,17.18,0,0,1,22.42,15l.06.19"
    />
    <path
      class="cls-1"
      d="M10.63,10.12A4.73,4.73,0,0,0,11,8.17,4.78,4.78,0,1,0,6.26,13a4.67,4.67,0,0,0,1.55-.26"
    />
  </svg>
</template>
