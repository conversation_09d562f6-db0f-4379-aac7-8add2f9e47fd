<script setup lang="ts">
const props = defineProps<{
  width?: number;
  height?: number;
}>();
</script>

<template>
  <!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools -->
  <svg
    :height="props.height ?? props.width ?? '100%'"
    :width="props.width ?? props.height ?? '100%'"
    class="fill-current"
    viewBox="0 0 96 96"
  >
    <title />
    <path
      d="M95.7073,36.5936a6.0024,6.0024,0,0,0-4.8457-4.084L64.963,28.742,53.3789,5.2751c-2.0156-4.1016-8.7422-4.1016-10.7578,0L31.037,28.742,5.1384,32.51A5.9979,5.9979,0,0,0,1.81,42.74L20.5545,61.01,16.1307,86.8029a5.9994,5.9994,0,0,0,8.7071,6.3223L48,80.9493,71.1622,93.1252a5.9994,5.9994,0,0,0,8.7071-6.3223L75.4455,61.01,94.19,42.74A6.0013,6.0013,0,0,0,95.7073,36.5936ZM64.8106,54.6171a5.9988,5.9988,0,0,0-1.7226,5.3087l2.9,16.9219L50.7949,68.8614a5.994,5.994,0,0,0-5.59,0L30.0116,76.8477l2.9-16.9219a5.9988,5.9988,0,0,0-1.7226-5.3087L18.8963,42.6347,35.8827,40.162A5.9891,5.9891,0,0,0,40.4,36.8807L48,21.4881l7.6,15.3926a5.9891,5.9891,0,0,0,4.5176,3.2813l16.9864,2.4727Z"
    />
  </svg>
</template>
