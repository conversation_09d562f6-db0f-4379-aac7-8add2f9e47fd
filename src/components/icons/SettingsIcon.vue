<script setup lang="ts">
const props = defineProps<{
  width?: number;
  height?: number;
}>();
</script>

<template>
  <!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools -->
  <svg
    :height="props.height ?? props.width ?? '100%'"
    :width="props.width ?? props.height ?? '100%'"
    class="fill-current"
    viewBox="0 0 1200 1200"
    version="1.1"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
    xmlns:cc="http://creativecommons.org/ns#"
    xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
    xmlns:svg="http://www.w3.org/2000/svg"
    xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
    xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
    sodipodi:docname="cog-alt.svg"
    inkscape:version="0.48.4 r9939"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
  >
    <sodipodi:namedview
      inkscape:cy="530.4508"
      inkscape:cx="1150.9031"
      inkscape:zoom="0.26339286"
      showgrid="false"
      id="namedview30"
      guidetolerance="10"
      gridtolerance="10"
      objecttolerance="10"
      borderopacity="1"
      bordercolor="#666666"
      pagecolor="#ffffff"
      inkscape:current-layer="svg2"
      inkscape:window-maximized="1"
      inkscape:window-y="24"
      inkscape:window-height="876"
      inkscape:window-width="1535"
      inkscape:pageshadow="2"
      inkscape:pageopacity="0"
      inkscape:window-x="65"
    >
    </sodipodi:namedview>
    <path
      id="path4485"
      inkscape:connector-curvature="0"
      d="M600,0C268.629,0,0,268.629,0,600s268.629,600,600,600s600-268.629,600-600
S931.371,0,600,0z M542.871,234.375h114.258l11.06,110.303c22.729,6.054,44.277,14.988,64.16,26.514l85.84-70.166l80.786,80.786
l-70.166,85.84c11.525,19.883,20.46,41.432,26.514,64.16l110.303,11.06v114.258l-110.303,11.133
c-6.054,22.709-14.997,44.22-26.514,64.087l70.166,85.84l-80.786,80.786l-85.913-70.166c-19.874,11.516-41.371,20.464-64.087,26.514
l-11.06,110.303H542.871l-11.133-110.303c-22.696-6.05-44.155-15.007-64.014-26.514l-85.913,70.166l-80.786-80.786l70.166-85.913
c-11.507-19.858-20.463-41.317-26.514-64.014l-110.303-11.133V542.871l110.303-11.06c6.05-22.716,14.998-44.213,26.514-64.087
l-70.166-85.913l80.786-80.786l85.84,70.166c19.867-11.517,41.378-20.46,64.087-26.514L542.871,234.375z M600,494.312
c-58.374,0-105.688,47.314-105.688,105.688S541.626,705.688,600,705.688S705.688,658.374,705.688,600S658.374,494.312,600,494.312z"
    />
  </svg>
</template>
