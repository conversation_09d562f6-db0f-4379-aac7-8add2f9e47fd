<script setup lang="ts">
const props = defineProps<{
  width?: number;
  height?: number;
}>();
</script>

<template>
  <!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools -->
  <svg
    :height="props.height ?? props.width ?? '100%'"
    :width="props.width ?? props.height ?? '100%'"
    class="fill-current"
    viewBox="0 0 60 60"
  >
    <g>
      <path
        d="M59,0H1C0.448,0,0,0.447,0,1v58c0,0.553,0.448,1,1,1h58c0.552,0,1-0.447,1-1V1C60,0.447,59.552,0,59,0z M18.686,50.828
    l-3.756-1.975l-3.756,1.975l0.717-4.183l-3.039-2.963l4.2-0.61l1.878-3.806l1.878,3.806l4.2,0.61l-3.039,2.963L18.686,50.828z
    M17.968,31.202l0.717,4.183l-3.756-1.975l-3.756,1.975l0.717-4.183l-3.039-2.963l4.2-0.61l1.878-3.806l1.878,3.806l4.2,0.61
    L17.968,31.202z M17.968,16.119l0.717,4.183l-3.756-1.975l-3.756,1.975l0.717-4.183l-3.039-2.963l4.2-0.61l1.878-3.806l1.878,3.806
    l4.2,0.61L17.968,16.119z M47,48H23v-4h24V48z M47,33H23v-4h24V33z M47,18H23v-4h24V18z"
      />
    </g>
  </svg>
</template>
