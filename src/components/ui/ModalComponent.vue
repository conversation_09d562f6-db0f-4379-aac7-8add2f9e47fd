<script setup lang="ts">
import { ref } from 'vue';

import CrossCircularFilledIcon from '@/components/icons/CrossCircularFilledIcon.vue';
import IconButton from '@/components/ui/IconButton.vue';

const emits = defineEmits(['close', 'open']);

const props = withDefaults(
  defineProps<{
    shouldCloseOnSelect?: boolean;
    shouldCloseWhenClickedOutside?: boolean;
    title: string;
  }>(),
  {
    shouldCloseOnClick: false,
    shouldCloseWhenClickedOutside: true,
    title: '',
  },
);

const dialog = ref<HTMLDialogElement>();
const isOpen = ref(false);

function close() {
  isOpen.value = false;
  dialog.value?.close();

  emits('close');
}

function open() {
  dialog.value?.showModal();
  isOpen.value = true;

  emits('open');
}

function onClick(e: MouseEvent) {
  if (props.shouldCloseWhenClickedOutside) {
    closeWhenClickedOutside(e);
  }

  if (props.shouldCloseOnClick) {
    const dialog = e.currentTarget as HTMLDialogElement;

    dialog?.close();
  }
}

function closeWhenClickedOutside(e: MouseEvent) {
  const dialog = e.currentTarget as HTMLDialogElement;
  const elementBeingClicked = e.target as HTMLElement;

  if (dialog.contains(elementBeingClicked) && dialog !== elementBeingClicked) return;

  dialog.close();
}

defineExpose({ close, open, isOpen });
</script>

<template>
  <dialog
    ref="dialog"
    class="secondary w-full md:w-2/3 md:max-w-lg h-2/3 overflow-hidden rounded-xl open:flex flex-col"
    @click.stop="onClick"
  >
    <div class="p-4 sticky top-0 border-b-2 grid grid-cols-[1fr_auto] items-center rtl drop-shadow-2xl bg-inherit">
      <h3 class="farsi text-3xl font-semibold text-center ms-8">{{ props.title }}</h3>

      <IconButton
        @click.stop="close"
        class="max-h-8 lg:max-h-10"
      >
        <CrossCircularFilledIcon />
      </IconButton>
    </div>

    <div
      v-if="isOpen"
      class="flex-1 overflow-auto"
    >
      <slot />
    </div>
  </dialog>
</template>

<style scoped>
dialog::backdrop {
  @apply backdrop-blur-sm;
}
</style>
