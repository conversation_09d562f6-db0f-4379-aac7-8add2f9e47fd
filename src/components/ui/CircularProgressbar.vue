<script setup lang="ts">
const props = defineProps<{
  percentage: number;
  color?: string;
}>();
</script>

<template>
  <div
    class="circular-progressbar relative aspect-square rounded-full"
    :style="{
      '--progress': `${props.percentage}%`,
      '--color': props.color || 'rgba(0, 0, 0, 1)',
    }"
  >
    <slot />
  </div>
</template>

<style scoped>
.circular-progressbar {
  &::before {
    content: '';

    @apply absolute inset-0 rounded-full z-50;

    background-image: conic-gradient(
      var(--color) var(--progress, 0),
      rgba(0, 0, 0, 0) var(--progress, 0)
    );

    --hole-size: 60%;

    -webkit-mask: radial-gradient(circle at center, transparent var(--hole-size), black var(--hole-size));
    mask: radial-gradient(circle at center, transparent var(--hole-size), black var(--hole-size));
  }
}
</style>
