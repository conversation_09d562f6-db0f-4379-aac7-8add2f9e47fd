<script setup lang="ts">
withDefaults(
  defineProps<{
    dataToggle?: 'active' | 'pending';
  }>(),
  {
    dataToggle: 'pending',
  },
);
</script>
<template>
  <button
    class="rounded-full aspect-square flex justify-center"
    :data-toggle="dataToggle"
  >
    <slot />
  </button>
</template>

<style scoped>
button:hover {
  background-color: inherit;
  color: var(--color-primary-hover-background-color);
}
</style>
