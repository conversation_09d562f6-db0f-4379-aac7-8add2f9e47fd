<script setup lang="ts">
import { useProcessStore } from '@/stores/ProcessStore.ts';

const processStore = useProcessStore();
</script>

<template>
  <div
    class="fixed left-2 right-2 p-4 rounded-t-lg shadow-sm primary transition-all duration-150 ease-in-out max-h-48 overflow-auto"
    :class="processStore.activeProcesses.length ? 'bottom-0' : '-bottom-16'"
  >
    <h5
      v-if="processStore.activeProcesses.length"
      class="sticky top-0 uppercase border-b mb-4 py-2 backdrop-blur-sm"
    >
      {{ processStore.activeProcesses.length }} processes
    </h5>
    <ul>
      <li
        v-for="p in processStore.activeProcesses"
        :key="p.id"
        class="passive"
      >
        <dl>
          <dt>{{ p.name }}</dt>
          <dd>{{ p.progress.currentValue }} %</dd>
        </dl>
      </li>
    </ul>
  </div>
</template>
