<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  title?: string;
  paragraphCollections?: string[][];
  contentLanguage?: string;
}>();

const contentLanguageClass = computed(() => {
  switch (props.contentLanguage) {
    case 'farsi':
      return 'farsi';
    case 'arabi':
    default:
      return 'dua';
  }
});
</script>

<template>
  <article class="m-auto h-full overflow-hidden flex flex-col primary p-2 rtl">
    <h1
      class="text-3xl p-4 secondary arabic title drop-shadow-xl"
      v-if="props.title"
    >
      {{ props.title }}
    </h1>

    <section
      class="flex-1 overflow-auto space-y-8 pt-8"
      v-if="props.paragraphCollections?.length"
    >
      <div
        class="flex flex-col md:flex-row gap-2"
        v-for="index in Math.max(...props.paragraphCollections.map(c => c.length))"
        :key="`paragraph-${index}`"
        :class="contentLanguageClass"
      >
        <p
          class="flex-1 text-start pe-1 md:pe-8 dua arabic"
          v-if="props.paragraphCollections[0]?.length > index"
        >
          {{ props.paragraphCollections[0][index] }}
        </p>
        <p
          class="flex-1 text-start pe-1 md:pe-8 dua farsi"
          v-if="props.paragraphCollections[1]?.length > index"
        >
          {{ props.paragraphCollections[1][index] }}
        </p>
      </div>
    </section>
  </article>
</template>
