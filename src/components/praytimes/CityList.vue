<script setup lang="ts">
import ProgressIndicator from '@/components/ui/ProgressIndicator.vue';
import { computed, onUpdated, ref } from 'vue';

defineEmits(['select']);

const props = defineProps<{
  dataQueryKey: string;
  filter: string;
  inProgress: Boolean;
  items: any[];
  itemKey: string;
  itemLabel: string;
  selectedItem: any;
}>();

const cityList = ref<HTMLUListElement>();

const filteredItems = computed(() => {
  if (!props || !props.filter) return props.items;

  const regexp = new RegExp(Array.from(props.filter).join('.*'), 'i');

  return props.items?.filter(item => regexp.test(item[props.itemLabel])) ?? [];
});

const keyPrefix = window.crypto.randomUUID?.() ?? window.crypto.getRandomValues(new Uint32Array(1))[0];

onUpdated(() => {
  cityList.value?.scroll(0, 0);
});
</script>

<template>
  <ul
    class="scroll-smooth"
    style="position: unset"
    ref="cityList"
  >
    <ProgressIndicator v-if="inProgress" />

    <li
      v-for="item in filteredItems"
      :key="`${keyPrefix}-${item[itemKey]}`"
      class="py-4 pl-8 pr-2 cursor-pointer transition-all ease"
      :class="{
        'secondary passive sticky top-2 bottom-2 drop-shadow-lg selected rounded-xl z-10': item === selectedItem,
      }"
      @click.stop="$emit('select', item)"
      :data-query="`${item[dataQueryKey]}`"
    >
      <span class="w-full">
        <span
          v-if="item === selectedItem"
          class="absolute left-0"
        >
          &rarr;
        </span>
        <span>{{ item[itemLabel] }}</span>
      </span>
    </li>
  </ul>
</template>
