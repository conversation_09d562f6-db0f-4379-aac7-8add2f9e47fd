<script setup lang="ts">
import type Continent from '@/models/Continent.ts';

const emit = defineEmits(['change']);

const props = defineProps<{
  continents: Continent[];
  selected: Continent | undefined;
}>();

function onChange(event: Event) {
  const continentId = parseInt((event.target as HTMLSelectElement).value);

  const continent = props.continents.find(({ id }) => id === continentId);

  emit('change', continent);
}
</script>

<template>
  <select
    name="continent"
    title="continent"
    class="bg-teal-900 rounded px-4 md:px-8 transition-all ease-in-out"
    @click.stop=""
    @change="onChange"
  >
    <option
      v-for="continent in props.continents"
      :key="continent.id"
      :value="continent.id"
      :selected="continent.id == selected?.id"
    >
      {{ continent.name }}
    </option>
  </select>
</template>

<style>
select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAUCAMAAACtdX32AAAAdVBMVEUAAAD///8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAhMdQaAAAAJ3RSTlMAAAECAwQGBwsOFBwkJTg5RUZ4eYCHkJefpaytrsXGy8zW3+Do8vNn0bsyAAAAYElEQVR42tXROwJDQAAA0Ymw1p9kiT+L5P5HVEi3qJn2lcPjtIuzUIJ/rhIGy762N3XaThqMN1ZPALsZPEzG1x8LrFL77DHBnEMxBewz0fJ6LyFHTPL7xhwzWYrJ9z22AqmQBV757MHfAAAAAElFTkSuQmCC);
  background-position: 100%;
  background-repeat: no-repeat;
  padding: 0.5rem;
}
</style>
