<script setup lang="ts">
import HistoryService from '@/services/general/history-service.ts';
import { ref } from 'vue';

const isClearing = ref(false);
const isCleared = ref(false);

function clearHistory() {
  isClearing.value = true;

  try {
    HistoryService.clearHistory();
    isCleared.value = true;

    // Reset after 2 seconds
    setTimeout(() => {
      isCleared.value = false;
      isClearing.value = false;
    }, 2000);
  } catch (error) {
    console.error('Error clearing history:', error);
    isClearing.value = false;
  }
}
</script>

<template>
  <button
    @click="clearHistory"
    class="clear-history-btn"
    :disabled="isClearing"
  >
    <span v-if="isCleared">History Cleared!</span>
    <span v-else-if="isClearing">Clearing...</span>
    <span v-else>Clear History</span>
  </button>
</template>

<style scoped>
.clear-history-btn {
  padding: 0.5rem 1rem;
  background-color: #33776B;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.clear-history-btn:hover:not(:disabled) {
  background-color: #2a6359;
}

.clear-history-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
</style>
