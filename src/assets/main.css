@import "./base.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {}

@layer components {
  * {
    user-select: none;
  }

  a, button, select {
    cursor: pointer;
  }

  body {
    background-color: var(--color-body-background);
    color: white;
  }

  #app {
    font-weight: normal;
  }

  .router-link-active {
    @apply secondary pointer-events-none rounded-md;
  }

  .arabic {
    direction: rtl;
    line-height: 225%;

    &.quran {
      font-family: "nabi";
    }

    &.dua {
      font-family: "nabi";
      font-size: 125%;
    }

    &.title {
      font-family: "estedad-semibold";
    }
  }

  .farsi {
    direction: rtl;
    font-family: "estedad-medium";
  }

  .icon {
    aspect-ratio: 1;
    @apply h-6 md:h-8;
  }

  .tab-title {
    font-family: estedad;
  }

  h1.farsi {
    font-family: "farsi-title";
  }

  .rtl {
    direction: rtl;
  }

  .ltr {
    direction: ltr;
  }

  body:not(:has(.touch-screen)) button:hover {
    background-color: var(--color-primary-hover-background-color);
    border-color: var(--color-primary-hover-text-color);
    color: var(--color-primary-hover-text-color);
  }

  button[data-toggle="active"] {
    outline-color: var(--color-primary-hover-background-color);
    outline-style: solid;
    outline-width: 0.125rem;
    outline-offset: -0.125rem;
  }

  button:active {
    background-color: initial;
    border-color: initial;
    color: initial;
  }

  li:not(.passive) {
    cursor: pointer;
  }

  li:not(.passive):hover {
    background-color: var(--color-primary-hover-background-color);
    color: var(--color-primary-hover-text-color);
    .text-highlight {
      color: var(--color-secondary-text-color);
    }
  }

  /* ol:not(.non-separated) > li:not(:first-child):not(.selected),
  ul:not(.non-separated) > li:not(:first-child):not(.selected) { */
  ol:not(.non-separated) > li:not(:first-child):not(.selected),
  ul:not(.non-separated) > li:not(:first-child):not(.selected) {
    border-top-style: dashed;
    border-top-width: thin;
    border-color: black;
  }

  ol:not(.non-separated) > li.selected,
  ul:not(.non-separated) > li.selected {
    border-top-style: dashed;
    border-bottom-style: dashed;
    border-top-width: thin;
    border-bottom-width: thin;
    border-color: black;
  }

  .touch-screen {
    ol, ul {
      align-content: flex-end;
    }
  }

  /* ol:not(.non-separated) > li:not(:first-child).selected + li,
  ul:not(.non-separated) > li:not(:first-child).selected + li {
    border-top: none;
  } */

  @supports selector(::webkit-scrollbar) {
    ::webkit-scrollbar {
      width: 2em;
    }

    ::webkit-scrollbar-track {
      background: black;
      border-radius: 100vw;
      margin-block: 0.5em;
    }

    ::webkit-scrollbar-thumb {
      background: silver;
      border-radius: 100vw;
    }

    ::webkit-scrollbar-thumb:hover {
      background: white;
    }
  }

  @supports (scrollbar-color: red blue) {
    * {
      scrollbar-color: var(--color-primary-foreground-color) var(--color-primary-background-color);
    }
  }
}

@layer utilities {
  /* Styles according to Bootstrap */
  .text-primary {

  }

  .text-secondary {

  }

  .text-success {

  }

  .text-danger {

  }

  .text-warning {

  }

  .text-info {

  }

  .text-light {

  }

  .text-dark {

  }

  .text-body {

  }

  .text-muted {

  }

  /* Styles according to myself */
  .primary {
    background-color: var(--color-primary-background-color);
    color: var(--color-primary-text-color);
    .text-highlight {
      color: var(--color-highlight);
    }
  }

  .secondary {
    background-color: var(--color-secondary-background-color);
    color: var(--color-secondary-text-color);
    .text-highlight {
      color:moccasin;
    }
  }
}
