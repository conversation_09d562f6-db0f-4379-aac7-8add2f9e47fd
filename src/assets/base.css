/* color palette from <https://github.com/vuejs/theme> */

:root {
  --dark-backgound: #252A2D;
  --dark-background-2: rgb(5, 58, 58);
  --tavoos-orange: rgb(245, 158, 11);
  --teal-dark: hsl(180, 100%, 15%);
  --teal-light: hsl(180, 80%, 27%);
  --teal-normal: rgb(0, 121, 107);
}

/* semantic color variables for this project */
:root {
  --color-active-link: var(--dark-backgound);
  --color-body-background: var(--dark-backgound);
  --color-highlight: var(--tavoos-orange);
  --color-primary-background-color: white;
  --color-primary-hover-background-color: var(--tavoos-orange);
  --color-primary-hover-text-color: black;
  --color-primary-text-color:  var(--teal-normal);;
  --color-secondary-background-color: var(--teal-normal);
  --color-secondary-hover-background-color: var(--teal-dark);
  --color-secondary-hover-text-color: white;
  --color-secondary-text-color: white;
}

@font-face {
  font-family: "farsi";
  src: url("/fonts/Iranian Sans.ttf");
}

@font-face {
  font-family: "farsi-title";
  src: url("/fonts/Digi Titr Plus Bold Circle.ttf");
}

@font-face {
  font-family: "me-quran";
  src: url("/fonts/me_quran.ttf");
}

@font-face {
  font-family: "amiri-quran";
  src: url("/fonts/AmiriQuran-Regular.ttf");
}

@font-face {
  font-family: "sahel";
  src: url("/fonts/Sahel-FD.woff");
}

@font-face {
  font-family: "neirizi";
  src: url("/fonts/Neirizi Regular.ttf");
}

@font-face {
  font-family: "estedad-extrabold";
  src: url("/fonts/Estedad-ExtraBold.woff2");
}

@font-face {
  font-family: "estedad-bold";
  src: url("/fonts/Estedad-Bold.woff2");
}

@font-face {
  font-family: "estedad-semibold";
  src: url("/fonts/Estedad-SemiBold.woff2");
}

@font-face {
  font-family: "estedad-extralight";
  src: url("/fonts/Estedad-ExtraLight.woff2");
}

@font-face {
  font-family: "estedad-light";
  src: url("/fonts/Estedad-Light.woff2");
}

@font-face {
  font-family: "estedad-thin";
  src: url("/fonts/Estedad-Thin.woff2");
}

@font-face {
  font-family: "estedad-regular";
  src: url("/fonts/Estedad-Regular.woff2");
}

@font-face {
  font-family: "estedad-medium";
  src: url("/fonts/Estedad-Medium.woff2");
}

@font-face {
  font-family: "nabi";
  src: url("/fonts/Nabi.ttf");
}

@font-face {
  font-family: "quran-taha";
  src: url("/fonts/QuranTaha.ttf");
}

@font-face {
  font-family: "uthman-tn-bold";
  src: url("/fonts/UthmanTNB_v2-0.ttf");
}

.aspect-square {
  aspect-ratio: 1;
}
