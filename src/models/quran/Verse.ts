import { IChapter, IPage, Point, IVerse } from '@/types/types-quran.ts';
import { useChapterStore } from '@/stores/quran/chapter-store.ts';
import StringUtils from '@/utils/string-utils.ts';
import { usePageStore } from '@/stores/quran/page-store.ts';

interface VerseConstructorOptions {
  chapterIndex: number;
  endSignCoordinates?: Point;
  hizbQuarter: number;
  index: number;
  indexInQuran: number;
  juz: number;
  manzil: number;
  ruku: number;
  text: string;
}

export default class Verse implements IVerse {
  chapterIndex: number;
  endSignCoordinates: Point | undefined;
  hizbQuarter: number;
  index: number;
  indexInQuran: number;
  juz: number;
  manzil: number;
  ruku: number;
  text: string;

  private _purifiedText: string;
  private _page: IPage | undefined;

  constructor(options: VerseConstructorOptions) {
    this.chapterIndex = options.chapterIndex;
    this.endSignCoordinates = options.endSignCoordinates ?? undefined;
    this.hizbQuarter = options.hizbQuarter;
    this.index = options.index;
    this.indexInQuran = options.indexInQuran;
    this.juz = options.juz;
    this.manzil = options.manzil;
    this.ruku = options.ruku;
    this.text = options.text;

    this._purifiedText = new StringUtils().purify(this.text);
  }

  get chapter(): IChapter {
    return useChapterStore().chapters[this.chapterIndex - 1] as IChapter;
  }

  get page(): IPage | undefined {
    this._page ??= this.findPage(usePageStore().pages);

    return this._page;
  }

  get purifiedText(): string {
    return this._purifiedText;
  }

  get purifiedWords(): string[] {
    return this.purifiedText.split(' ');
  }

  get id(): string {
    return `${this.chapter.index}:${this.index}`;
  }

  private findPage(pages: IPage[]): IPage {
    const middleIndex = Math.floor(pages.length / 2);
    const middlePage = pages[middleIndex];

    if (middlePage.verses.includes(this)) return middlePage;

    const pagesToSearch = middlePage.verses[0].indexInQuran > this.indexInQuran
      ? pages.slice(0, middleIndex)
      : pages.slice(middleIndex + 1);

    return this.findPage(pagesToSearch);
  }
}
