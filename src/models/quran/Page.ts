import { IPage, IVerse, IVerseAddress } from '@/types/types-quran.ts';
import { useChapterStore } from '@/stores/quran/chapter-store.ts';

interface PageConstructorOptions {
  index: number;
  verseAddresses: IVerseAddress[];
}

export default class Page implements IPage {
  index: number;
  verseAddresses: IVerseAddress[];

  private _verses: IVerse[] = [];

  constructor(options: PageConstructorOptions) {
    this.index = options.index;
    this.verseAddresses = options.verseAddresses;
  }

  get verses(): IVerse[] {
    if (this._verses.length === 0) {
      const chapterStore = useChapterStore();

      this._verses = this.verseAddresses.map(address => {
        const chapter = chapterStore.chapters[address.chapterIndex - 1];
        const verse = chapter.verses[address.verseIndex - 1];

        return verse;
      });
    }

    return this._verses;
  }
}
