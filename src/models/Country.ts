import Continent                from './Continent.ts';
import TavoosModel              from './TavoosModel.ts';
import type City                from '@/models/City.ts';
import { api }                  from '@/services/db/prayTimesDbService.ts';
import { ref }                  from 'vue';
import { useCityStore }         from '@/stores/CityStore.ts';
import { useContinentStore }    from '@/stores/ContinentStore.ts';
import { useCountryStore }      from '@/stores/CountryStore.ts';

interface ConstructorObject {
  id: number;
  code: string;
  name: string;
  continentId: number;
}

const _flagUrl = ref('');
const DONT_ASK_FOR_FLAG = true;

export default class Country extends TavoosModel {
  id: number = 0;
  code: string = '';
  name: string = '';
  continentId: number = 0;

  constructor(constructorObject: ConstructorObject) {
    const existingCountry = useCountryStore().countries.find(({ id }) => id === constructorObject.id);

    if (existingCountry) return existingCountry as any;

    super();

    this.id = constructorObject.id;
    this.code = constructorObject.code;
    this.name = constructorObject.name;
    this.continentId = constructorObject.continentId;
  }

  get summerTimeMonths(): number[] {
    switch (this.continent.code) {
      case 'AF':
        switch (this.name) {
          case 'Egypt':
            return [4, 5, 6, 7, 8, 9, 10];
          case 'Morocco':
          case 'Western Sahara':
            // Shit! It is applied all the time, then removed during Ramadan
            return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
          default:
            return [];
        }
      case 'AN':
        return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
      case 'AS':
        return [];
      case 'EU':
        switch (this.name) {
          case 'Belarus':
          case 'Russia':
            return [];
          default:
            return [3, 4, 5, 6, 7, 8, 9, 10];
        }
      case 'NA':
        switch (this.name) {
          case 'Bahamas':
          case 'Cuba':
          case 'Haiti':
          case 'Mexico':
          case 'United States':
            return [3, 4, 5, 6, 7, 8, 9, 10, 11];
          default:
            return [];
        }
      case 'OC':
        switch (this.name) {
          case 'Australia':
            return [1, 2, 3, 4, 10, 11, 12];
          case 'Zealand':
            return [1, 2, 3, 4, 9, 10, 11, 12];
          default:
            return [];
        }
      case 'SA':
        switch (this.name) {
          case 'Chile':
            return [1, 2, 3, 4, 10, 11, 12];
          case 'Paraguay':
            return [1, 2, 3, 10, 11, 12];
          default:
            return [];
        }
      default:
        return [];
    }
  }

  get continent(): Continent {
    return useContinentStore().continents.find(({ id }) => id === this.continentId)!;
  }

  get cities(): City[] {
    return useCityStore().citiesOfCountry(this);
  }

  get flagUrl(): string {
    if (DONT_ASK_FOR_FLAG) {
      // return `https://www.countryflagicons.com/FLAT/64/${this.code}.png`;
      return `https://flagsapi.com/${this.code}/flat/64.png`;
    }

    if (!_flagUrl.value) {
      this.getFlagUrl();
    }

    return _flagUrl.value;
  }

  async getFlagUrl() {
    const res = await fetch(`${api()}/countries/${this.id}/flag`, {
      headers: {
        accept: 'application/json, text/plain, */*',
      },
    });

    _flagUrl.value = (await res.json()).url;
  }
}
