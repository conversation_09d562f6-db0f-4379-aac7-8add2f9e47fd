import Progress from '@/models/application/Progress.ts';
import { useProcessStore } from '@/stores/ProcessStore.ts';
import { reactive } from 'vue';

const processStore = useProcessStore();
export default class Process {
  client: any;
  id: number;
  name: string;
  progress: Progress;

  private static _lastId: number = 0;

  constructor(name: string, client: any = undefined, progress: Progress | undefined = undefined) {
    if (!name) throw Error('Invalid name for Process');

    this.id = Process._nextId();
    this.name = name;
    this.client = client;

    this.progress = reactive(progress || new Progress());

    processStore.addProcess(this);
  }

  private static _nextId = () => ++Process._lastId;

  complete() {
    this.progress.complete();
  }

  reset() {
    this.progress.reset();
  }
}
