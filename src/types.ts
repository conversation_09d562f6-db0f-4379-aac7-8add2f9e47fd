import type City from '@/models/City.ts';

export interface PrayersTimeTable {
  emsak: Date | null;
  fajr: Date | null;
  sunrise: Date | null;
  dhohr: Date | null;
  aser: Date | null;
  sunset: Date | null;
  maghreb: Date | null;
  isha: Date | null;
  midnight: Date | null;
}

export interface PrayersSchedule {
  city: City;
  date: Date;
  timeTable: PrayersTimeTable;
}

export interface PrayersTimesSource {
  name: string;
  url: string | null;
}

export type LayoutDirection = 'horizontal' | 'vertical';
