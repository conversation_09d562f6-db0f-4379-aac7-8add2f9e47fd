import type { Component } from 'vue';

export interface Point {
  x: number;
  y: number;
}

export interface IChapter {
  index: number;
  title: string;
  englishTitle: string;
  englishTitleTranslation: string;
  verses: IVerse[];
}

export interface MenuItem {
  icon: Component;
  index: number;
  title: string;
  action: Function;
  disabled?: boolean;
}

export interface IPage {
  index: number;
  verses: IVerse[];
}

export interface IVerse {
  chapter: IChapter;
  endSignCoordinates?: Point | undefined;
  hizbQuarter: number;
  index: number;
  indexInQuran: number;
  juz: number;
  manzil: number;
  page: IPage | undefined;
  purifiedText: string;
  ruku: number;
  text: string;
  id?: string;
  purifiedWords? : string[];
}

export interface IVerseAddress {
  chapterIndex: number;
  verseIndex: number;
}
