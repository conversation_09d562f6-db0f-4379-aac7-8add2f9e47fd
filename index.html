<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/img/icons/favicon-32x32.png" />
    <link rel="manifest" href="/manifest.json">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <meta name="description" content="The original resources of Islam." />
    <title>Tavoos</title>
  </head>
  <body>
    <div id="app"
      class="
        relative
        h-[100dvh] overflow-hidden
        flex portrait:flex-col landscape:flex-row
        drop-shadow-2xl
        p-0
        sm:landscape:py-2 sm:portrait:px-2
        md:landscape:py-4 md:portrait:px-4
      "
    ></div>
    <script type="module" src="/src/main.ts"></script>
    <script>
      if ("serviceWorker" in navigator) {
        navigator.serviceWorker.register('/sw.js').then(function (reg) {
          console.log("Service worker has been registered for scope: " + reg.scope);
        });
      }
    </script>
  </body>
</html>
