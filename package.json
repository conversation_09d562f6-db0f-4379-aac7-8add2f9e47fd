{"name": "tavoos-vue3", "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "run-p type-check build-only", "preview": "vite preview --port 4173", "test:unit": "vitest --environment jsdom", "test:e2e": "start-server-and-test preview http://localhost:4173/ 'cypress open --e2e'", "test:e2e:ci": "start-server-and-test preview http://localhost:4173/ 'cypress run --e2e'", "build-only": "vite build", "type-check": "vue-tsc -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "pinia": "^2.1.7", "vue": "^3.4.13", "vue-router": "^4.2.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.6.1", "@types/jsdom": "^21.1.6", "@types/node": "^20.11.0", "@types/vue": "^2.0.0", "@vitejs/plugin-vue": "^5.0.3", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.16", "cypress": "^13.6.2", "eslint": "^8.56.0", "eslint-plugin-cypress": "^2.15.1", "eslint-plugin-vue": "^9.20.1", "jsdom": "^23.2.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.33", "prettier": "^3.2.1", "start-server-and-test": "^2.0.3", "tailwindcss": "^3.4.1", "typescript": "~5.3.3", "vite": "^5.0.11", "vitest": "^1.2.0", "vue-tsc": "^1.8.27"}}