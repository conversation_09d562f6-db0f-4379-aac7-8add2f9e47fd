<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline Document</title>
    <style>
      body {
        background: #252A2D;
        color: #33776B;
        font-family: system-ui, mono-space;
      }

      .progressbar.base {
        background: #33776B;
        width: min(18em, 100%);
        height: 0.3em;
        border-radius: 3px;
        display: block;
        overflow: hidden;
      }

      .progressbar.bar {
        background: orange;
        height: 100%;
        width: 100%;
        display: block;
        transform-origin: left;

        animation: expands 3s ease-in-out infinite;
      }
      
      @keyframes expands {
        0% {
          transform: scale(0, 1);
        }

        100% {
          transform: scale(1, 1);
        }
      }

      .label {
        margin-bottom: 0.3em;
      }
    </style>
  </head>

  <body>
    <h1>Offline</h1>

    <div class="label">Connecting...</div>

    <div class="progressbar base">
      <div class="progressbar bar">

      </div>
    </div>

    <script type="text/javascript">
      function goOnline() {
        window.removeEventListener('online', goOnline)

        window.location.replace('/');
      }
    </script>
  </body>
</html>
